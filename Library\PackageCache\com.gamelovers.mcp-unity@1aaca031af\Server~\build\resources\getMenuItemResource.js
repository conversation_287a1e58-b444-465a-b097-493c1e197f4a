import { McpUnityError, ErrorType } from '../utils/errors.js';
// Constants for the resource
const resourceName = 'get_menu_items';
const resourceUri = 'unity://menu-items';
const resourceMimeType = 'application/json';
/**
 * Creates and registers the Menu Items resource with the MCP server
 * This resource provides access to the Unity menu items
 *
 * @param server The MCP server instance to register with
 * @param mcpUnity The McpUnity instance to communicate with Unity
 * @param logger The logger instance for diagnostic information
 */
export function registerGetMenuItemsResource(server, mcpUnity, logger) {
    logger.info(`Registering resource: ${resourceName}`);
    // Register this resource with the MCP server
    server.resource(resourceName, resourceUri, {
        description: 'List of available menu items in Unity to execute',
        mimeType: resourceMimeType
    }, async () => {
        try {
            return await resourceHandler(mcpUnity);
        }
        catch (error) {
            logger.error(`Error handling resource ${resourceName}: ${error}`);
            throw error;
        }
    });
}
/**
 * <PERSON>les requests for menu items information from Unity
 *
 * @param mcpUnity The McpUnity instance to communicate with Unity
 * @returns A promise that resolves to the menu items data
 * @throws McpUnityError if the request to Unity fails
 */
async function resourceHandler(mcpUnity) {
    const response = await mcpUnity.sendRequest({
        method: resourceName,
        params: {}
    });
    if (!response.success) {
        throw new McpUnityError(ErrorType.RESOURCE_FETCH, response.message || 'Failed to fetch menu items from Unity');
    }
    return {
        contents: [{
                uri: resourceUri,
                mimeType: resourceMimeType,
                text: JSON.stringify(response.menuItems, null, 2)
            }]
    };
}
