Using pre-set license
Built from '2022.3/staging' branch; Version is '2022.3.47f1 (88c277b85d21) revision 8962679'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 15607 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\00ruanjian\00unity\2022.3.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
D:/BaiduNetdiskDownload/UnityProject/AutoColliderSetUp-master
-logFile
Logs/AssetImportWorker1.log
-srvPort
52298
Successfully changed project path to: D:/BaiduNetdiskDownload/UnityProject/AutoColliderSetUp-master
D:/BaiduNetdiskDownload/UnityProject/AutoColliderSetUp-master
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [9396]  Target information:

Player connection [9396]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 4256026182 [EditorId] 4256026182 [Version] 1048832 [Id] WindowsEditor(7,ANI9) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [9396] Host joined multi-casting on [***********:54997]...
Player connection [9396] Host joined alternative multi-casting on [***********:34997]...
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
Refreshing native plugins compatible for Editor in 37.10 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.47f1 (88c277b85d21)
[Subsystems] Discovering subsystems at path D:/00ruanjian/00unity/2022.3.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/BaiduNetdiskDownload/UnityProject/AutoColliderSetUp-master/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4060 Laptop GPU (ID=0x28a0)
    Vendor:   NVIDIA
    VRAM:     7957 MB
    Driver:   32.0.15.7628
Initialize mono
Mono path[0] = 'D:/00ruanjian/00unity/2022.3.47f1/Editor/Data/Managed'
Mono path[1] = 'D:/00ruanjian/00unity/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/00ruanjian/00unity/2022.3.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56596
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/00ruanjian/00unity/2022.3.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/00ruanjian/00unity/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.006832 seconds.
- Loaded All Assemblies, in  0.661 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 1781 ms
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.088 seconds
Domain Reload Profiling: 2747ms
	BeginReloadAssembly (320ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (74ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (56ms)
	LoadAllAssembliesAndSetupDomain (199ms)
		LoadAssemblies (318ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (196ms)
			TypeCache.Refresh (195ms)
				TypeCache.ScanAssembly (181ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (2089ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2045ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1879ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (116ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.994 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in D:\BaiduNetdiskDownload\UnityProject\AutoColliderSetUp-master\AutoColliderSetUp-master.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.142 seconds
Domain Reload Profiling: 2123ms
	BeginReloadAssembly (190ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (698ms)
		LoadAssemblies (579ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (242ms)
			TypeCache.Refresh (202ms)
				TypeCache.ScanAssembly (173ms)
			ScanForSourceGeneratedMonoScriptInfo (35ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (1143ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (935ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (28ms)
			SetLoadedEditorAssemblies (8ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (130ms)
			ProcessInitializeOnLoadAttributes (673ms)
			ProcessInitializeOnLoadMethodAttributes (91ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.07 seconds
Refreshing native plugins compatible for Editor in 28.86 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4061 Unused Serialized files (Serialized files now loaded: 0)
Unloading 25 unused Assets / (342.3 KB). Loaded Objects now: 4537.
Memory consumption went from 156.0 MB to 155.6 MB.
Total: 4.506400 ms (FindLiveObjects: 0.374100 ms CreateObjectMapping: 0.346100 ms MarkObjects: 3.600100 ms  DeleteObjects: 0.184600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 43315.766583 seconds.
  path: Assets/11.unity
  artifactKey: Guid(43d11ee6a86347a4e9ba27f19b3b4bb7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/11.unity using Guid(43d11ee6a86347a4e9ba27f19b3b4bb7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '0ecc98514ce12e6448cc4a31b410b28f') in 0.005183 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
