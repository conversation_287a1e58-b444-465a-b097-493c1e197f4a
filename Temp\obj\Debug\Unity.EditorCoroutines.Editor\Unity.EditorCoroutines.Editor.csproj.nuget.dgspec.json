{"format": 1, "restore": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.EditorCoroutines.Editor.csproj": {}}, "projects": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.EditorCoroutines.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.EditorCoroutines.Editor.csproj", "projectName": "Unity.EditorCoroutines.Editor", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.EditorCoroutines.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.EditorCoroutines.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj", "projectName": "UnityEditor.TestRunner", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\UnityEditor.TestRunner\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj", "projectName": "UnityEditor.UI", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\UnityEditor.UI\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj", "projectName": "UnityEngine.TestRunner", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\UnityEngine.TestRunner\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj", "projectName": "UnityEngine.UI", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\UnityEngine.UI\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}}