import * as z from 'zod';
import { McpUnityError, ErrorType } from '../utils/errors.js';
// Constants for the tool
const toolName = 'execute_menu_item';
const toolDescription = 'Executes a Unity menu item by path';
const paramsSchema = z.object({
    menuPath: z.string().describe('The path to the menu item to execute (e.g. "GameObject/Create Empty")')
});
/**
 * Creates and registers the Menu Item tool with the MCP server
 * This tool allows executing menu items in the Unity Editor
 *
 * @param server The MCP server instance to register with
 * @param mcpUnity The McpUnity instance to communicate with Unity
 * @param logger The logger instance for diagnostic information
 */
export function registerMenuItemTool(server, mcpUnity, logger) {
    logger.info(`Registering tool: ${toolName}`);
    // Register this tool with the MCP server
    server.tool(toolName, toolDescription, paramsSchema.shape, async (params) => {
        try {
            logger.info(`Executing tool: ${toolName}`, params);
            const result = await toolHandler(mcpUnity, params);
            logger.info(`Tool execution successful: ${toolName}`);
            return result;
        }
        catch (error) {
            logger.error(`Tool execution failed: ${toolName}`, error);
            throw error;
        }
    });
}
/**
 * Handles menu item execution requests
 *
 * @param mcpUnity The McpUnity instance to communicate with Unity
 * @param params The parameters for the tool
 * @returns A promise that resolves to the tool execution result
 * @throws McpUnityError if the request to Unity fails
 */
async function toolHandler(mcpUnity, params) {
    const { menuPath } = params;
    const response = await mcpUnity.sendRequest({
        method: toolName,
        params: { menuPath }
    });
    if (!response.success) {
        throw new McpUnityError(ErrorType.TOOL_EXECUTION, response.message || `Failed to execute menu item: ${menuPath}`);
    }
    return {
        content: [{
                type: response.type,
                text: response.message || `Successfully executed menu item: ${menuPath}`
            }]
    };
}
